# موقع إيدوم - متجر أدوات التجميل

موقع تجارة إلكترونية متخصص في بيع أدوات التجميل والعناية بالجمال، مصمم باللغة العربية مع دعم كامل للنص من اليمين إلى اليسار (RTL).

## المميزات

### 🎨 التصميم
- تصميم عصري وجذاب مخصص لمنتجات التجميل
- دعم كامل للغة العربية مع اتجاه النص RTL
- تصميم متجاوب يعمل على جميع الأجهزة (الهاتف، التابلت، الكمبيوتر)
- ألوان وردية أنيقة تناسب طبيعة المنتجات
- تأثيرات بصرية متقدمة وانيميشن سلس

### 🛍️ الوظائف
- **سلة التسوق**: إضافة المنتجات للسلة مع عداد تفاعلي
- **البحث**: بحث سريع في المنتجات
- **الفئات**: تصنيف المنتجات حسب النوع
- **المنتجات المميزة**: عرض أفضل المنتجات
- **الأكثر مبيعاً**: قائمة بأكثر المنتجات مبيعاً
- **العروض الخاصة**: عروض مع عداد تنازلي
- **نموذج الاتصال**: للتواصل مع العملاء
- **النشرة الإخبارية**: للاشتراك في التحديثات

### 📱 التفاعلية
- قائمة تنقل متجاوبة للهواتف المحمولة
- تمرير سلس بين الأقسام
- تأثيرات حركية عند التمرير
- إشعارات تفاعلية للمستخدم
- تأثيرات hover متقدمة

## الملفات

### `index.html`
الملف الرئيسي للموقع يحتوي على:
- هيكل HTML5 كامل
- دعم RTL للغة العربية
- جميع أقسام الموقع (الرأس، البطل، الفئات، المنتجات، العروض، من نحن، الاتصال، التذييل)
- ربط بمكتبات الخطوط والأيقونات

### `style.css`
ملف الأنماط الشامل يتضمن:
- تصميم متجاوب كامل
- أنماط للجميع العناصر
- تأثيرات بصرية وانيميشن
- ألوان متدرجة جذابة
- دعم للأجهزة المختلفة

### `script.js`
ملف JavaScript للتفاعلية:
- وظائف سلة التسوق
- القائمة المتجاوبة للهواتف
- التمرير السلس
- معالجة النماذج
- العد التنازلي للعروض
- تأثيرات الحركة
- البحث في المنتجات

## كيفية التشغيل

### الطريقة الأولى: خادم Python
```bash
# في مجلد المشروع
python -m http.server 8000
```
ثم افتح المتصفح على: `http://localhost:8000`

### الطريقة الثانية: خادم Node.js
```bash
# تثبيت http-server
npm install -g http-server

# تشغيل الخادم
http-server
```

### الطريقة الثالثة: فتح مباشر
يمكن فتح ملف `index.html` مباشرة في المتصفح

## الأقسام الرئيسية

### 1. الرأس (Header)
- شعار الموقع
- قائمة التنقل
- مربع البحث
- أيقونة سلة التسوق
- قائمة الهاتف المحمول

### 2. القسم البطل (Hero)
- عنوان رئيسي جذاب
- وصف المتجر
- زر دعوة للعمل
- صورة تمثيلية

### 3. الفئات (Categories)
- مكياج الوجه
- مكياج العيون
- أحمر الشفاه
- العناية بالبشرة

### 4. المنتجات المميزة
- عرض شبكي للمنتجات
- صور عالية الجودة
- تقييمات بالنجوم
- الأسعار مع الخصومات
- أزرار إضافة للسلة

### 5. العروض الخاصة
- بانر عرض جذاب
- عداد تنازلي للعرض
- زر تسوق سريع

### 6. الأكثر مبيعاً
- قائمة مرتبة بأفضل المنتجات
- ترقيم واضح
- صور مصغرة
- الأسعار

### 7. من نحن
- معلومات عن المتجر
- المميزات والخدمات
- صورة تعبيرية

### 8. الاتصال
- معلومات التواصل
- نموذج اتصال تفاعلي
- التحقق من صحة البيانات

### 9. التذييل (Footer)
- روابط سريعة
- خدمة العملاء
- وسائل التواصل الاجتماعي
- النشرة الإخبارية

## التقنيات المستخدمة

- **HTML5**: هيكل الموقع
- **CSS3**: التصميم والأنماط
- **JavaScript**: التفاعلية والوظائف
- **Font Awesome**: الأيقونات
- **Google Fonts**: خط Cairo للعربية
- **Unsplash**: الصور عالية الجودة

## المتطلبات

- متصفح ويب حديث
- اتصال بالإنترنت (للخطوط والصور)
- خادم ويب محلي (اختياري)

## الدعم

الموقع يدعم:
- جميع المتصفحات الحديثة
- الأجهزة المحمولة والتابلت
- أحجام الشاشات المختلفة
- اللغة العربية بشكل كامل

## التطوير المستقبلي

يمكن إضافة:
- قاعدة بيانات للمنتجات
- نظام دفع إلكتروني
- حسابات المستخدمين
- إدارة المخزون
- تتبع الطلبات
- تقييمات العملاء
- نظام الخصومات والكوبونات

---

تم تطوير هذا الموقع بعناية فائقة ليكون نموذجاً مثالياً لمتاجر التجميل الإلكترونية باللغة العربية.
